#!/bin/bash

# MongoDB Date Validator Setup Script
# This script sets up the environment and dependencies for the MongoDB Date Validator

echo "MongoDB Date Validator Setup"
echo "============================"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.7 or higher."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3."
    exit 1
fi

echo "✅ pip3 found: $(pip3 --version)"

# Install dependencies
echo ""
echo "Installing dependencies..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Failed to install dependencies. Please check the error messages above."
    exit 1
fi

# Make scripts executable
chmod +x mongo_date_validator.py
chmod +x test_validator.py
chmod +x config_example.py
chmod +x test_api_config.py
chmod +x test_curl_api.sh

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Usage Examples:"
echo "==============="
echo ""
echo "1. API Configuration (Recommended):"
echo "   python3 mongo_date_validator.py --tenant \"your_tenant\" --dealer-id \"your_dealer_id\""
echo ""
echo "2. Test API connection:"
echo "   python3 mongo_date_validator.py --test-api --tenant \"your_tenant\" --dealer-id \"your_dealer_id\""
echo ""
echo "3. Test API with curl:"
echo "   ./test_curl_api.sh"
echo ""
echo "4. Test API with Python:"
echo "   python3 test_api_config.py"
echo ""
echo "5. Fallback to config file:"
echo "   python3 mongo_date_validator.py --config production_config.json"
echo ""
echo "6. Direct connection string:"
echo "   python3 mongo_date_validator.py --connection-string \"mongodb://localhost:27017\""
echo ""
echo "7. Run tests (requires local MongoDB):"
echo "   python3 test_validator.py"
echo ""
echo "8. View help:"
echo "   python3 mongo_date_validator.py --help"
echo ""
echo "Configuration:"
echo "=============="
echo "The script now uses API-based configuration by default."
echo "Provide your tenant and dealer ID to fetch MongoDB connection details automatically."
echo ""
echo "Fallback options:"
echo "- Edit config.json for static configuration"
echo "- Sample configurations are available in config_samples/ directory"
echo ""
echo "For more information, see README.md"
