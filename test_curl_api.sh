#!/bin/bash

# Test script for API configuration using curl
# This script demonstrates how to test the API endpoint directly

# Configuration
TENANT="your_tenant"
DEALER_ID="your_dealer_id"
AUTH_TOKEN="ved"
API_BASE_URL="http://internal-prod-dms-pvt-1426699881.us-west-1.elb.amazonaws.com"

echo "MongoDB Date Validator - API Test with curl"
echo "============================================"
echo ""
echo "Configuration:"
echo "  Tenant: $TENANT"
echo "  Dealer ID: $DEALER_ID"
echo "  Auth Token: $AUTH_TOKEN"
echo ""

# Construct the API URL
API_URL="${API_BASE_URL}/config/v2/read/secondary/MONGO/DSE_DEFAULT/${TENANT}/${DEALER_ID}"

echo "API URL: $API_URL"
echo ""
echo "Testing API connection..."
echo "------------------------"

# Make the curl request
curl --location --request GET "${API_URL}?authToken=${AUTH_TOKEN}" \
     --header "Accept: application/json" \
     --header "Content-Type: application/json" \
     --connect-timeout 30 \
     --max-time 60 \
     --silent \
     --show-error \
     --write-out "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\nSize: %{size_download} bytes\n" | jq '.'

echo ""
echo "If you see valid JSON output above, the API is working correctly."
echo "You can now use the Python script with:"
echo "  python mongo_date_validator.py --tenant \"$TENANT\" --dealer-id \"$DEALER_ID\""
echo ""
echo "To test with the Python script directly:"
echo "  python mongo_date_validator.py --test-api --tenant \"$TENANT\" --dealer-id \"$DEALER_ID\""
