# MongoDB Date Validator

A comprehensive Python script that identifies MongoDB records containing invalid date fields which cause PyMongo DateOverflowError exceptions. The script efficiently scans large MongoDB databases to find dates with years 0000 or greater than 9999, which are not supported by Python's datetime module.

## Features

- **Async/Await Multithreading**: Separate processing for each tenant/collection for optimal performance
- **DateOverflowError Handling**: Implements try-catch approach with DateTimeMS fallback as recommended
- **Comprehensive Scanning**: Automatically detects and validates potential date fields across all collections
- **Batch Processing**: Configurable batch sizes for memory-efficient processing of large collections
- **Detailed Reporting**: CSV output with database, collection, record ID, field name, and invalid date value
- **Progress Tracking**: Real-time logging and progress updates during scanning
- **Flexible Configuration**: Command-line options for customizing scan parameters

## Installation

1. Install required dependencies:

```bash
pip install -r requirements.txt
```

2. Ensure you have access to your MongoDB instance and the appropriate connection credentials.

## Usage

### API Configuration Approach (Recommended)

The script now fetches MongoDB configuration dynamically from an API endpoint. This eliminates the need for static configuration files:

```bash
# Fetch configuration from API using tenant and dealer ID
python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id"

# Use custom auth token (default is 'ved')
python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id" --auth-token "custom_token"

# Test API connection before running validation
python mongo_date_validator.py --test-api --tenant "your_tenant" --dealer-id "your_dealer_id"
```

### Configuration File Approach (Fallback)

For environments where API access is not available, you can still use configuration files:

```bash
# Use custom config file
python mongo_date_validator.py --config production_config.json
```

### Direct Connection (Override)

```bash
# Direct connection string (bypasses API and config file)
python mongo_date_validator.py --connection-string "mongodb://localhost:27017"
```

### Advanced Usage Examples

```bash
# API configuration with command line overrides (command line takes precedence)
python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id" --batch-size 2000

# API configuration with specific databases and custom output
python mongo_date_validator.py \
  --tenant "your_tenant" \
  --dealer-id "your_dealer_id" \
  --databases "db1,db2,db3" \
  --output-file "custom_validation_report.csv" \
  --log-level DEBUG

# Pure command line approach (bypassing API and config file)
python mongo_date_validator.py \
  --connection-string "mongodb://localhost:27017" \
  --exclude-collections "system.users,system.roles" \
  --output-file "my_validation_report.csv" \
  --batch-size 2000 \
  --max-concurrent 20 \
  --save-summary
```

### Configuration Options

#### API Configuration

The script fetches MongoDB configuration from the following API endpoint:

```
GET http://internal-prod-dms-pvt-1426699881.us-west-1.elb.amazonaws.com/config/v2/read/secondary/MONGO/DSE_DEFAULT/<tenant>/<dealer_id>?authToken=<auth_token>
```

**API Parameters:**

- `tenant`: Your tenant identifier
- `dealer_id`: Your dealer identifier
- `auth_token`: Authentication token (default: "ved")

**Expected API Response Format:**
The API should return a JSON response containing MongoDB connection details. The script supports multiple response formats:

```json
{
  "connectionString": "********************************:port/database",
  "databases": "db1,db2,db3",
  "excludeCollections": "system.users,system.roles",
  "batchSize": 1000,
  "maxConcurrentCollections": 10
}
```

Or individual connection components:

```json
{
  "host": "mongodb-server",
  "port": 27017,
  "username": "user",
  "password": "pass",
  "database": "mydb"
}
```

#### Config File Format (config.json) - Fallback

```json
{
  "connection_string": "mongodb://localhost:27017",
  "databases": null,
  "exclude_collections": "system.users,system.roles,system.indexes",
  "batch_size": 1000,
  "max_concurrent_collections": 10,
  "output_file": "invalid_dates_report.csv",
  "sample_limit": 10,
  "log_level": "INFO",
  "no_summary": false,
  "save_summary": false
}
```

#### Command Line Options

**API Configuration (Primary):**

- `--tenant`: Tenant ID for API configuration fetch
- `--dealer-id`: Dealer ID for API configuration fetch
- `--auth-token`: Authentication token for API requests (default: "ved")
- `--test-api`: Test API connection and print response

**Configuration File (Fallback):**

- `--config`: Path to configuration JSON file (default: config.json)

**Direct Configuration (Override):**

- `--connection-string`: MongoDB connection string (overrides API/config)
- `--databases`: Comma-separated list of databases to scan
- `--exclude-collections`: Comma-separated list of collection names to exclude
- `--batch-size`: Number of documents to process in each batch (default: 1000)
- `--max-concurrent`: Maximum number of collections to process concurrently (default: 10)
- `--output-file`: Output CSV file name (default: invalid_dates_report.csv)
- `--sample-limit`: Number of sample invalid records per collection (default: 10)
- `--log-level`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `--no-summary`: Skip printing summary to console
- `--save-summary`: Save detailed summary report as JSON file

#### Sample Configuration Files

The `config_samples/` directory contains pre-configured examples:

- `production_config.json`: Production environment settings
- `development_config.json`: Development environment settings
- `high_performance_config.json`: Optimized for large databases
- `atlas_config.json`: MongoDB Atlas connection example

## Output Format

The script generates a CSV file with the following columns as specified in the use case:

- **Database Name**: Name of the MongoDB database
- **Collection Name**: Name of the collection containing the invalid record
- **RecordId**: MongoDB ObjectId or document identifier
- **Field Name**: Name of the field containing the invalid date
- **Invalid Date Value**: The actual invalid date value found
- **Error Type**: Description of why the date is invalid

## How It Works

1. **Connection**: Establishes async connection to MongoDB using Motor driver
2. **Discovery**: Automatically discovers all databases and collections (or uses specified ones)
3. **Field Detection**: Identifies potential date fields based on naming patterns and data types
4. **Validation Strategy**:
   - First attempts normal document iteration to trigger DateOverflowError
   - If DateOverflowError occurs, switches to DateTimeMS codec for that batch
   - Validates each date field for years 0000 or >9999
5. **Concurrent Processing**: Uses async/await to process multiple collections simultaneously
6. **Reporting**: Generates comprehensive CSV report and optional JSON summary

## Performance Considerations

- **Batch Size**: Larger batch sizes (1000-2000) are generally more efficient for large collections
- **Concurrency**: Adjust `--max-concurrent` based on your MongoDB server capacity and network
- **Memory Usage**: The script processes documents in batches to minimize memory footprint
- **Network**: Uses efficient cursor-based iteration to minimize data transfer

## Error Handling

The script includes robust error handling for:

- MongoDB connection failures
- Collection access permissions
- Document parsing errors
- Date validation exceptions
- Network timeouts and interruptions

## Troubleshooting

### Common Issues

1. **Connection Timeout**: Increase server selection timeout in connection string
2. **Memory Issues**: Reduce batch size or max concurrent collections
3. **Permission Errors**: Ensure MongoDB user has read access to all target databases
4. **Large Collections**: Use smaller batch sizes and enable DEBUG logging for progress tracking

### MongoDB Connection String Examples

```bash
# Local MongoDB
"mongodb://localhost:27017"

# MongoDB with authentication
"*******************************************"

# MongoDB Atlas
"mongodb+srv://username:<EMAIL>"

# Replica Set
"mongodb://host1:27017,host2:27017,host3:27017/database?replicaSet=myReplicaSet"
```

## License

This script is provided as-is for MongoDB date validation purposes.
