# Changelog

## Version 2.0.0 - API Configuration Support

### Major Changes

**🚀 API-Based Configuration (New Primary Method)**
- Added support for dynamic configuration fetching via HTTP API
- Eliminates the need for static config.json files in most use cases
- Fetches MongoDB connection details from: `http://internal-prod-dms-pvt-1426699881.us-west-1.elb.amazonaws.com/config/v2/read/secondary/MONGO/DSE_DEFAULT/<tenant>/<dealer_id>`

### New Features

**API Configuration**
- `--tenant`: Specify tenant ID for API configuration fetch
- `--dealer-id`: Specify dealer ID for API configuration fetch  
- `--auth-token`: Authentication token for API requests (default: "ved")
- `--test-api`: Test API connection and print response for debugging

**Enhanced Error Handling**
- Comprehensive API error handling with detailed error messages
- Fallback support to config files when API is unavailable
- Better validation of API responses

**Testing Tools**
- `test_api_config.py`: Python script to test API functionality
- `test_curl_api.sh`: Shell script to test API with curl command
- Built-in API connection testing with `--test-api` flag

### Updated Dependencies

- Added `requests>=2.25.0` for HTTP API calls

### Configuration Priority (New Hierarchy)

1. **Command line arguments** (highest priority - overrides everything)
2. **API configuration** (fetched using --tenant and --dealer-id)
3. **Config file** (fallback when API not available)
4. **Default values** (lowest priority)

### Usage Examples

**New API-based usage (Recommended):**
```bash
# Basic API usage
python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id"

# Test API connection
python mongo_date_validator.py --test-api --tenant "your_tenant" --dealer-id "your_dealer_id"

# API with custom auth token
python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id" --auth-token "custom_token"
```

**Fallback methods still supported:**
```bash
# Config file fallback
python mongo_date_validator.py --config production_config.json

# Direct connection string
python mongo_date_validator.py --connection-string "mongodb://localhost:27017"
```

### Breaking Changes

- Config file is no longer the primary configuration method (now fallback)
- Default behavior now expects `--tenant` and `--dealer-id` parameters
- Output file naming now includes tenant and dealer ID when using API configuration

### Migration Guide

**From config.json to API:**
1. Identify your tenant and dealer ID
2. Replace: `python mongo_date_validator.py --config myconfig.json`
3. With: `python mongo_date_validator.py --tenant "your_tenant" --dealer-id "your_dealer_id"`

**Testing your migration:**
1. Test API connection: `python mongo_date_validator.py --test-api --tenant "your_tenant" --dealer-id "your_dealer_id"`
2. Use curl test: `./test_curl_api.sh` (after editing with your values)
3. Use Python test: `python test_api_config.py` (after editing with your values)

### Files Modified

- `mongo_date_validator.py`: Added API configuration support
- `requirements.txt`: Added requests dependency
- `README.md`: Updated documentation for API usage
- `setup.sh`: Updated setup script with new usage examples

### Files Added

- `test_api_config.py`: Python API testing script
- `test_curl_api.sh`: Shell script for curl-based API testing
- `CHANGELOG.md`: This changelog file

### Backward Compatibility

- All existing config.json files continue to work as fallback
- All existing command-line arguments remain functional
- No changes to output format or validation logic
