#!/usr/bin/env python3
"""
Test script for API configuration functionality
This script demonstrates how to test the API connection and fetch configuration
"""

import sys
import json
from mongo_date_validator import test_api_connection, fetch_config_from_api

def main():
    """Test API configuration functionality."""
    
    # Example usage - replace with your actual tenant and dealer ID
    tenant = "example_tenant"
    dealer_id = "example_dealer_id"
    auth_token = "ved"
    
    print("MongoDB Date Validator - API Configuration Test")
    print("=" * 50)
    
    # Test 1: API Connection Test
    print("\n1. Testing API Connection...")
    print(f"Tenant: {tenant}")
    print(f"Dealer ID: {dealer_id}")
    print(f"Auth Token: {auth_token}")
    print("-" * 30)
    
    try:
        success = test_api_connection(tenant, dealer_id, auth_token)
        if success:
            print("✓ API connection test PASSED")
        else:
            print("✗ API connection test FAILED")
            return False
    except Exception as e:
        print(f"✗ API connection test ERROR: {e}")
        return False
    
    # Test 2: Configuration Fetch Test
    print("\n2. Testing Configuration Fetch...")
    print("-" * 30)
    
    try:
        config = fetch_config_from_api(tenant, dealer_id, auth_token)
        print("✓ Configuration fetch PASSED")
        print("\nFetched Configuration:")
        print(json.dumps(config, indent=2))
        
        # Validate required fields
        if 'connection_string' in config and config['connection_string']:
            print("✓ Connection string found in configuration")
        else:
            print("✗ Connection string missing or empty in configuration")
            return False
            
    except Exception as e:
        print(f"✗ Configuration fetch ERROR: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("All API tests completed successfully!")
    print("\nNext steps:")
    print("1. Replace 'example_tenant' and 'example_dealer_id' with your actual values")
    print("2. Run the validator with: python mongo_date_validator.py --tenant <your_tenant> --dealer-id <your_dealer_id>")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
